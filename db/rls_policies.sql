-- Enable RLS on all document-related tables
ALTER TABLE collaborative_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_presence ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view documents they own or have permission to" ON collaborative_documents;
DROP POLICY IF EXISTS "Users can create documents" ON collaborative_documents;
DROP POLICY IF EXISTS "Users can update documents they own" ON collaborative_documents;
DROP POLICY IF EXISTS "Users can delete documents they own" ON collaborative_documents;

DROP POLICY IF EXISTS "Users can view comments on accessible documents" ON document_comments;
DROP POLICY IF EXISTS "Users can create comments on accessible documents" ON document_comments;
DROP POLICY IF EXISTS "Users can update their own comments" ON document_comments;
DROP POLICY IF EXISTS "Users can delete their own comments" ON document_comments;

DROP POLICY IF EXISTS "Users can view versions of accessible documents" ON document_versions;
DROP POLICY IF EXISTS "Users can create versions of accessible documents" ON document_versions;

DROP POLICY IF EXISTS "Document owners can manage permissions" ON document_permissions;
DROP POLICY IF EXISTS "Users can view permissions for documents they have access to" ON document_permissions;

DROP POLICY IF EXISTS "Users can manage presence for accessible documents" ON document_presence;

-- Collaborative Documents Policies
CREATE POLICY "Users can view documents they own or have permission to" ON collaborative_documents
    FOR SELECT USING (
        auth.uid() = created_by OR
        (metadata->>'isPublic')::boolean = true
    );

CREATE POLICY "Users can create documents" ON collaborative_documents
    FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update documents they own" ON collaborative_documents
    FOR UPDATE USING (auth.uid() = created_by);

CREATE POLICY "Users can delete documents they own" ON collaborative_documents
    FOR DELETE USING (auth.uid() = created_by);

-- Document Comments Policies
CREATE POLICY "Users can view comments on accessible documents" ON document_comments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM collaborative_documents
            WHERE id = document_comments.document_id
            AND (
                auth.uid() = created_by OR
                (metadata->>'isPublic')::boolean = true
            )
        )
    );

CREATE POLICY "Users can create comments on accessible documents" ON document_comments
    FOR INSERT WITH CHECK (
        auth.uid() = document_comments.user_id AND
        EXISTS (
            SELECT 1 FROM collaborative_documents
            WHERE id = document_comments.document_id
            AND (
                auth.uid() = created_by OR
                (metadata->>'isPublic')::boolean = true
            )
        )
    );

CREATE POLICY "Users can update their own comments" ON document_comments
    FOR UPDATE USING (auth.uid() = document_comments.user_id);

CREATE POLICY "Users can delete their own comments" ON document_comments
    FOR DELETE USING (
        auth.uid() = document_comments.user_id OR
        EXISTS (
            SELECT 1 FROM collaborative_documents
            WHERE id = document_comments.document_id
            AND auth.uid() = created_by
        )
    );

-- Document Versions Policies
CREATE POLICY "Users can view versions of accessible documents" ON document_versions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM collaborative_documents
            WHERE id = document_versions.document_id
            AND (
                auth.uid() = created_by OR
                (metadata->>'isPublic')::boolean = true
            )
        )
    );

CREATE POLICY "Users can create versions of accessible documents" ON document_versions
    FOR INSERT WITH CHECK (
        auth.uid() = created_by AND
        EXISTS (
            SELECT 1 FROM collaborative_documents
            WHERE id = document_versions.document_id
            AND auth.uid() = created_by
        )
    );

-- Document Permissions Policies
CREATE POLICY "Document owners can manage permissions" ON document_permissions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM collaborative_documents
            WHERE id = document_permissions.document_id
            AND auth.uid() = created_by
        )
    );

CREATE POLICY "Users can view permissions for documents they have access to" ON document_permissions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM collaborative_documents
            WHERE id = document_permissions.document_id
            AND auth.uid() = created_by
        )
    );

-- Document Presence Policies
CREATE POLICY "Users can manage presence for accessible documents" ON document_presence
    FOR ALL USING (
        auth.uid() = document_presence.user_id AND
        EXISTS (
            SELECT 1 FROM collaborative_documents
            WHERE id = document_presence.document_id
            AND (
                auth.uid() = created_by OR
                (metadata->>'isPublic')::boolean = true
            )
        )
    );

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON collaborative_documents TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON document_comments TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON document_versions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON document_permissions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON document_presence TO authenticated;
